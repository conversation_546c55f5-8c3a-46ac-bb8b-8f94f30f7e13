.relative {
    position: relative !important;
}

.text-white {
    color: white;
}

.service-content {
	position: absolute;
    top: 46%;
    left: 0;
    right: 0;
    transform: translate(0, -50%);
    z-index: 2;
    padding: 0 10%;
}

.img-overlay {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 1;
}

.contact-row {
  display: flex;
  gap: 32px;
  align-items: stretch;
  flex-wrap: wrap;
}
.contact-box {
  background: #f9f9f9;
  padding: 24px;
  border-radius: 16px;
  height: 100%;
  flex: 1 1 320px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}